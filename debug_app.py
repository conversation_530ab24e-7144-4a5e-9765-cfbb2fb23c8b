import os

import numpy as np
import pandas as pd
from dotenv import load_dotenv
from flask import Flask, request
from flask_restx import Api, Resource, fields

from lib.larsson_indicator_calculator import LarssonIndicatorCalculator

load_dotenv()
port = int(os.getenv('PORT', 5556))

app = Flask(__name__)
api = Api(app, doc='/docs/', title='Larsson Indicator API', version='1.0')

# Swagger models
ohlc_model = api.model('OHLC', {
    'open': fields.Float(required=True),
    'high': fields.Float(required=True),
    'low': fields.Float(required=True),
    'close': fields.Float(required=True),
    'volume': fields.Float(),
    'marketCap': fields.Float(),
    'timestamp': fields.String(),
    'name': fields.String()
})

result_model = api.model('Result', {
    'open': fields.Float(),
    'high': fields.Float(),
    'low': fields.Float(),
    'close': fields.Float(),
    'volume': fields.Float(),
    'marketCap': fields.Float(),
    'timestamp': fields.String(),
    'name': fields.String(),
    'hl2': fields.Float(),
    'smma_15': fields.Float(),
    'smma_19': fields.Float(),
    'smma_25': fields.Float(),
    'smma_29': fields.Float(),
    'p1': fields.Boolean(),
    'p2': fields.Boolean(),
    'p3': fields.Boolean(),
    'color': fields.String()
})

def validate_ohlc_data(data):
    print(f"DEBUG validate_ohlc_data: Input data: {data}")
    print(f"DEBUG validate_ohlc_data: Input type: {type(data)}")
    
    if not isinstance(data, list) or not data:
        error = "Input must be a non-empty array"
        print(f"DEBUG validate_ohlc_data: Error - {error}")
        return error

    required_fields = ['open', 'high', 'low', 'close']
    optional_fields = ['volume', 'marketCap', 'timestamp', 'name']
    
    for i, item in enumerate(data):
        print(f"DEBUG validate_ohlc_data: Processing item {i}: {item}")
        print(f"DEBUG validate_ohlc_data: Item type: {type(item)}")
        
        if not isinstance(item, dict):
            error = "Each item must be an object"
            print(f"DEBUG validate_ohlc_data: Error - {error}")
            return error

        # Validate required fields
        for field in required_fields:
            print(f"DEBUG validate_ohlc_data: Checking required field '{field}'")
            if field not in item:
                error = f"Missing required field: {field}"
                print(f"DEBUG validate_ohlc_data: Error - {error}")
                return error
            
            print(f"DEBUG validate_ohlc_data: Field '{field}' value: {item[field]} (type: {type(item[field])})")
            
            # Handle both numeric and string representations of numbers (BigDecimal from Java)
            try:
                value = float(item[field]) if isinstance(item[field], str) else item[field]
                print(f"DEBUG validate_ohlc_data: Converted '{field}' to: {value} (type: {type(value)})")
                if not isinstance(value, (int, float)) or value <= 0:
                    error = f"Field {field} must be a positive number"
                    print(f"DEBUG validate_ohlc_data: Error - {error}")
                    return error
                # Update the item with the converted value for consistency
                item[field] = value
            except (ValueError, TypeError) as e:
                error = f"Field {field} must be a valid number: {e}"
                print(f"DEBUG validate_ohlc_data: Error - {error}")
                return error

        # Validate optional numeric fields
        for field in ['volume', 'marketCap']:
            if field in item and item[field] is not None:
                print(f"DEBUG validate_ohlc_data: Checking optional field '{field}': {item[field]} (type: {type(item[field])})")
                try:
                    value = float(item[field]) if isinstance(item[field], str) else item[field]
                    print(f"DEBUG validate_ohlc_data: Converted optional '{field}' to: {value} (type: {type(value)})")
                    if not isinstance(value, (int, float)) or value < 0:
                        error = f"Field {field} must be a non-negative number"
                        print(f"DEBUG validate_ohlc_data: Error - {error}")
                        return error
                    # Update the item with the converted value for consistency
                    item[field] = value
                except (ValueError, TypeError) as e:
                    error = f"Field {field} must be a valid number: {e}"
                    print(f"DEBUG validate_ohlc_data: Error - {error}")
                    return error

        # Convert high and low to float for comparison if they're strings
        try:
            high_val = float(item['high']) if isinstance(item['high'], str) else item['high']
            low_val = float(item['low']) if isinstance(item['low'], str) else item['low']
            print(f"DEBUG validate_ohlc_data: Comparing high ({high_val}) vs low ({low_val})")
            if high_val < low_val:
                error = "High price cannot be lower than low price"
                print(f"DEBUG validate_ohlc_data: Error - {error}")
                return error
        except (ValueError, TypeError) as e:
            error = f"High and low prices must be valid numbers: {e}"
            print(f"DEBUG validate_ohlc_data: Error - {error}")
            return error

    print("DEBUG validate_ohlc_data: Validation passed!")
    return None

@api.route('/monitoring/health')
class HealthCheck(Resource):
    def get(self):
        return {'status': 'OK'}


@api.route('/larsson/calculate')
class LarssonCalculate(Resource):
    @api.expect([ohlc_model])
    @api.marshal_list_with(result_model)
    def post(self):
        try:
            data = request.get_json()
            print(f"DEBUG: Received data: {data}")
            print(f"DEBUG: Data type: {type(data)}")
            if data and len(data) > 0:
                print(f"DEBUG: First item: {data[0]}")
                print(f"DEBUG: First item type: {type(data[0])}")

            error = validate_ohlc_data(data)
            if error:
                print(f"DEBUG: Validation error: {error}")
                api.abort(400, error)

            df = pd.DataFrame(data)
            calculator = LarssonIndicatorCalculator()
            result_df = calculator.calculate(df).replace({np.nan: None})
            return result_df.to_dict(orient='records')

        except Exception as e:
            print(f"DEBUG: Exception occurred: {e}")
            import traceback
            traceback.print_exc()
            api.abort(500, str(e))

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=port)
