import pandas as pd


class LarssonIndicatorCalculator:

    # //@version=5
    # indicator(title='CTO Line', shorttitle='CTO', overlay=true, timeframe='')
    #
    # smma(src, length) =>
    # smma = 0.0
    # sma = ta.sma(src, length)
    # smma := na(smma[1]) ? sma : (smma[1] * (length - 1) + src) / length
    # smma
    #
    # v1 = smma(hl2, 15)
    # m1 = smma(hl2, 19)
    # m2 = smma(hl2, 25)
    # v2 = smma(hl2, 29)
    #
    # p2 = v1 < m1 != v1 < v2 or m2 < v2 != v1 < v2
    # p3 = not p2 and v1 < v2
    # p1 = not p2 and not p3
    #
    # c = p1 ? color.rgb(255, 230, 0, 29) : p2 ? color.rgb(178, 183, 197) : color.rgb(33, 149, 243, 24)
    # line1 = plot(v1, 'Line 1', color=c)
    # line2 = plot(v2, 'Line 2', color=c)
    # fill(line1, line2, color=c, transp=90)

    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        source = df.copy()[::-1]

        source['hl2'] = (source['high'] + source['low']) / 2

        hl2 = source['hl2']

        for idx, i in enumerate(self.smma(hl2, 15).tolist()[::-1]):
            source.at[idx, 'smma_15'] = i

        for idx, i in enumerate(self.smma(hl2, 19).tolist()[::-1]):
            source.at[idx, 'smma_19'] = i

        for idx, i in enumerate(self.smma(hl2, 25).tolist()[::-1]):
            source.at[idx, 'smma_25'] = i

        for idx, i in enumerate(self.smma(hl2, 29).tolist()[::-1]):
            source.at[idx, 'smma_29'] = i

        # p2 = v1 < m1 != v1 < v2 or m2 < v2 != v1 < v2
        source['p2'] = ((source['smma_15'] < source['smma_19']) != (source['smma_15'] < source['smma_29'])) | \
                       ((source['smma_25'] < source['smma_29']) != (source['smma_15'] < source['smma_29']))

        # p3 = not p2 and v1 < v2
        source['p3'] = ~source['p2'] & (source['smma_15'] < source['smma_29'])

        # p1 = not p2 and not p3
        source['p1'] = ~source['p2'] & ~source['p3']

        # c = p1 ? color.rgb(255, 230, 0, 29) : p2 ? color.rgb(178, 183, 197) : color.rgb(33, 149, 243, 24)
        source['color'] = source.apply(
            lambda row: 'gold' if row['p1'] else 'gray' if row[
                'p2'] else 'blue',
            axis=1)

        return source

    def smma(self, series, length):
        smma_series = pd.Series(index=series.index, dtype=float)
        # Calculate the initial SMA and set it as the first value of SMMA
        initial_sma = series.iloc[:length].mean()
        smma_series[length - 1] = initial_sma
        # Calculate the rest of the SMMA values
        for i in range(length, len(series)):
            smma_series[i] = (smma_series[i - 1] * (length - 1) + series[i]) / length
        return smma_series
