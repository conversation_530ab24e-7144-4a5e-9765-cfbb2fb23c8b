# Larsson Indicator API

Flask microservice that calculates Larsson/CTO Line indicators using SMMA analysis.

## Quick Start

```bash
docker network create docker-network
docker-compose up --build -d
```

## API

- **Health**: `GET /monitoring/health`
- **Calculate**: `POST /la<PERSON><PERSON>/calculate`
- **Docs**: http://localhost:6501/docs/

### Example

```bash
curl -X POST -H "Content-Type: application/json" \
  -d '[{"open":0.06,"high":0.07,"low":0.05,"close":0.06}]' \
  http://localhost:6501/larsson/calculate
```

## Algorithm

Based on TradingView Pine Script:
- HL2: `(high + low) / 2`
- SMMA periods: 15, 19, 25, 29
- Signals: P1 (gold/bullish), P2 (gray/neutral), P3 (blue/bearish)

## Development

```bash
pip install -r requirements.txt
python app.py
```
