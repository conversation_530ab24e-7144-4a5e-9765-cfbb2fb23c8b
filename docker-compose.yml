version: '3.8'
services:
  lars<PERSON>_line_api:
    build: .
    networks:
      - docker-network
    ports:
      - "6501:5556"
    environment:
      - PORT=5556
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    volumes:
      - .:/app  # Mount source code for development hot-reload
    command: ["python", "-u", "./app.py"]  # -u for unbuffered output

networks:
  docker-network:
    external: true
