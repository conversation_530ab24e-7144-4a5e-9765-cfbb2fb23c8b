import unittest

import pandas as pd

from lib.la<PERSON><PERSON>_indicator_calculator import LarssonIndicatorCalculator


class TestLarssonIndicatorCalculator(unittest.TestCase):
    calculator = LarssonIndicatorCalculator()

    def test_calculate(self):
        data = pd.read_json('./btc_usd_data_for_all_time_quotes_2024_01_04.json')
        result = self.calculator.calculate(data)
        self.assertEqual(len(result), 4922)

        self.assertEqual(result.iloc[0].smma_15, 42198.64223278242)
        self.assertEqual(result.iloc[0].smma_29, 40050.31360679523)


if __name__ == '__main__':
    unittest.main()
